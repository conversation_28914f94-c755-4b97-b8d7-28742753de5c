import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { ContactModal } from "@/components/shared/contacts/contact-modal";
import type { CreateContactPayload } from "@/lib/apis/contacts";
import type { AddStudyContactPayload } from "@/lib/apis/studies/types";

import { useAddStudyContact } from "./hooks/use-contacts-mutations";

export const AddContactCard = () => {
  const { mutateAsync: addContact, isPending: isAddingContact } =
    useAddStudyContact();
  const [showModalAddContact, setShowModalAddContact] = useState(false);
  const params = useParams();
  const studyId = params.id as string;

  const onSubmit = async (data: AddStudyContactPayload) => {
    await addContact({ ...data, studyId });
    setShowModalAddContact(false);
  };

  return (
    <>
      <Card
        className="group min-h-48 cursor-pointer transition-all hover:bg-gray-200 dark:hover:bg-gray-700 [&>div]:p-4"
        onClick={() => setShowModalAddContact(true)}
      >
        <div className="text-normal flex items-center justify-center gap-2 text-gray-500">
          <IoMdAdd className="size-5" />
          <div>Add Contact</div>
        </div>
      </Card>
      <ContactModal
        show={showModalAddContact}
        onClose={() => setShowModalAddContact(false)}
        onSubmit={onSubmit as (data: CreateContactPayload) => void}
        isLoading={isAddingContact}
      />
    </>
  );
};
