import {
  endOfDay,
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  startOfDay,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subQuarters,
  subWeeks,
  subYears,
} from "date-fns";
import { DateRange } from "react-day-picker";

export type DateOptions =
  | "today"
  | "yesterday"
  | "last_week"
  | "last_month"
  | "last_quarter"
  | "last_year"
  | "custom"
  | "";

const createDateRange = (from: Date, to: Date) => ({
  fromDate: startOfDay(from).toISOString(),
  toDate: endOfDay(to).toISOString(),
});

export const calculateDateRange = (
  option: DateOptions | "",
  customRange?: DateRange,
) => {
  const now = new Date();

  if (!option || option === "today") {
    return createDateRange(now, now);
  }

  if (option === "yesterday") {
    const yesterday = subDays(now, 1);
    return createDateRange(yesterday, yesterday);
  }

  if (option === "last_week") {
    const lastWeek = subWeeks(now, 1);
    const weekOptions = { weekStartsOn: 1 as const };
    return createDateRange(
      startOfWeek(lastWeek, weekOptions),
      endOfWeek(lastWeek, weekOptions),
    );
  }

  if (option === "last_month") {
    const lastMonth = subMonths(now, 1);
    return createDateRange(startOfMonth(lastMonth), endOfMonth(lastMonth));
  }

  if (option === "last_quarter") {
    const lastQuarter = subQuarters(now, 1);
    return createDateRange(
      startOfQuarter(lastQuarter),
      endOfQuarter(lastQuarter),
    );
  }

  if (option === "last_year") {
    const lastYear = subYears(now, 1);
    return createDateRange(startOfYear(lastYear), endOfYear(lastYear));
  }

  return {
    fromDate: customRange?.from
      ? startOfDay(customRange.from).toISOString()
      : undefined,
    toDate: customRange?.to
      ? endOfDay(customRange.to).toISOString()
      : undefined,
  };
};
