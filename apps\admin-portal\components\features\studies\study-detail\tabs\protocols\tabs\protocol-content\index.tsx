import { <PERSON><PERSON> } from "flowbite-react";
import { useState } from "react";
import { IoMdDownload } from "react-icons/io";
import { MdFileUpload } from "react-icons/md";
import { z } from "zod";

import { LoadingDocument } from "@/components/shared/document-viewers/loading-document";
import { PDFViewer } from "@/components/shared/document-viewers/pdf-viewer";
import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { MonacoEditor } from "@/components/ui/form/monaco-editor";
import { Protocol } from "@/lib/apis/protocols";
import { downloadBlob } from "@/lib/utils";

import { useDownloadProtocol } from "../../hooks/use-protocols-mutations";
import { useProtocol } from "../../hooks/use-protocols-queries";
import { useDownloadedProtocol } from "../../hooks/use-protocols-queries";
import { useAddProtocolContent } from "./hooks/use-protocol-content-mutations";
import { useProtocolContent } from "./hooks/use-protocol-content-queries";
import { ModalUploadProtocol } from "./modal-upload-protocol";

const schema = z.object({
  content: z
    .string({ required_error: "Content is required" })
    .min(1, "Content is required"),
});

type Props = {
  selectedProtocol: Protocol | null;
};

export const ProtocolContentTab = ({ selectedProtocol }: Props) => {
  const { data: content, isLoading: isContentLoading } = useProtocolContent(
    selectedProtocol?.id,
  );

  const { mutateAsync: editContent, isPending } = useAddProtocolContent(
    selectedProtocol?.id,
  );

  async function onSubmit(data: z.infer<typeof schema>) {
    await editContent({
      protocolId: selectedProtocol?.id as string,
      content: data.content,
    });
  }

  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const { data: protocol } = useProtocol(selectedProtocol?.id || "");
  const { mutateAsync: downloadProtocol, isPending: isDownloading } =
    useDownloadProtocol();
  const { data, isPending: isPendingPdf } = useDownloadedProtocol(
    selectedProtocol?.id,
  );
  const handleDownloadProtocol = async (protocolId: string) => {
    const { url } = await downloadProtocol(protocolId);
    const response = await fetch(url);
    const blob = await response.blob();
    downloadBlob(
      blob,
      selectedProtocol?.name ? `${selectedProtocol.name}.pdf` : "protocol.pdf",
    );
  };
  if (!selectedProtocol) return null;

  return (
    <div className="p-4 pt-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold dark:text-gray-400">
          Protocol Content {selectedProtocol && `(${selectedProtocol.name})`}
        </h2>
        <div className="mb-4 flex flex-col justify-end gap-4 sm:flex-row">
          <Button
            disabled={protocol?.isPublished}
            variant="primary"
            onClick={() => setIsUploadModalOpen(true)}
          >
            <MdFileUpload />
            Upload PDF
          </Button>
          <Button
            disabled={protocol?.isPublished}
            variant="primary"
            isLoading={isDownloading}
            onClick={async () =>
              await handleDownloadProtocol(selectedProtocol?.id as string)
            }
          >
            <IoMdDownload />
            Download PDF
          </Button>
        </div>
      </div>

      <div className="grid h-[55vh] max-h-[60vh] grid-cols-2 grid-rows-1 overflow-hidden">
        <div className="overflow-y-auto">
          {isPendingPdf ? (
            <LoadingDocument />
          ) : (
            data?.url && <PDFViewer key={data?.url} url={data?.url} />
          )}
        </div>
        <div>
          {isContentLoading ? (
            <div className="grid h-full place-content-center">
              <Spinner size="xl" color="blue" className="fill-primary-500" />
            </div>
          ) : (
            <Form
              key={selectedProtocol?.id}
              onSubmit={onSubmit}
              schema={schema}
              defaultValues={{
                content: content?.content ?? "",
              }}
              className="flex h-full flex-col gap-4"
            >
              <MonacoEditor
                containerClassName="flex-1"
                height="100%"
                name="content"
              />
              <div className="flex justify-end">
                <Button
                  disabled={protocol?.isPublished}
                  isLoading={isPending}
                  variant="primary"
                  type="submit"
                >
                  Save
                </Button>
              </div>
            </Form>
          )}
        </div>
      </div>
      <ModalUploadProtocol
        isOpen={isUploadModalOpen}
        onClose={() => {
          setIsUploadModalOpen(false);
        }}
        selectedProtocol={selectedProtocol}
      />
    </div>
  );
};
