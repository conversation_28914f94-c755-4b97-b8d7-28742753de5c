import { useQuery } from "@tanstack/react-query";
import { SortingState } from "@tanstack/react-table";
import { differenceInDays } from "date-fns";
import { parseAsString, useQueryState } from "nuqs";

import api from "@/lib/apis";
import type {
  DocumentAnalyticsFilters,
  DocumentDetailFilters,
} from "@/lib/apis/document-analytics";
import { MetadataParams } from "@/lib/apis/types";

import { TAKE_50 } from "../../../../configurations/hooks/use-data-integrity-queries";
import { useDocumentAnalyticsFilters } from "./use-document-analytics-filters";

const documentAnalyticsKeys = {
  all: () => ["document-analytics"] as const,

  allSummary: () => [...documentAnalyticsKeys.all(), "summary"] as const,
  totalDocuments: (filters: DocumentAnalyticsFilters) =>
    [
      ...documentAnalyticsKeys.allSummary(),
      "total-documents",
      filters,
    ] as const,
  pendingReview: (filters: DocumentAnalyticsFilters) =>
    [...documentAnalyticsKeys.allSummary(), "pending-review", filters] as const,
  volumeOverTime: (filters: DocumentAnalyticsFilters) =>
    [
      ...documentAnalyticsKeys.allSummary(),
      "volume-over-time",
      filters,
    ] as const,
  originationType: (filters: DocumentAnalyticsFilters) =>
    [
      ...documentAnalyticsKeys.allSummary(),
      "origination-type",
      filters,
    ] as const,

  allDetail: () => [...documentAnalyticsKeys.all(), "detail"] as const,
  allDocumentsList: () =>
    [...documentAnalyticsKeys.allDetail(), "all-documents"] as const,
  allDocuments: (filters: DocumentDetailFilters) =>
    [...documentAnalyticsKeys.allDocumentsList(), filters] as const,
  allPendingDocumentsList: () =>
    [...documentAnalyticsKeys.allDetail(), "pending-documents"] as const,
  pendingDocuments: (filters: DocumentDetailFilters) =>
    [...documentAnalyticsKeys.allPendingDocumentsList(), filters] as const,
  allWorkflow: () => [...documentAnalyticsKeys.all(), "workflow"] as const,
  sourceDocumentStatusFunnel: (filters: DocumentAnalyticsFilters) =>
    [
      ...documentAnalyticsKeys.allWorkflow(),
      "source-document-status-funnel",
      filters,
    ] as const,
  isfArtifactStatusBreakdown: (filters: DocumentAnalyticsFilters) =>
    [
      ...documentAnalyticsKeys.allWorkflow(),
      "isf-artifact-status-breakdown",
      filters,
    ] as const,
  agingDocuments: (
    filters: DocumentAnalyticsFilters & {
      page?: number;
    },
  ) =>
    [
      ...documentAnalyticsKeys.allWorkflow(),
      "aging-documents",
      filters,
    ] as const,

  // Site Performance keys
  allSitePerformance: () =>
    [...documentAnalyticsKeys.all(), "site-performance"] as const,
  sitePerformanceLeaderboard: (filters?: MetadataParams) =>
    [
      ...documentAnalyticsKeys.allSitePerformance(),
      "leaderboard",
      filters,
    ] as const,
  inactiveSites: (filters?: MetadataParams) =>
    [
      ...documentAnalyticsKeys.allSitePerformance(),
      "inactive-sites",
      filters,
    ] as const,
  siteDocExchange: (filters?: MetadataParams) =>
    [
      ...documentAnalyticsKeys.allSitePerformance(),
      "site-doc-exchange",
      filters,
    ] as const,
};

export const useTotalDocuments = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.totalDocuments(filters),
    queryFn: () => api.documentAnalytics.getTotalDocuments(filters),
    placeholderData: (prev) => prev,
  });
};

export const useDocumentsPendingReview = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.pendingReview(filters),
    queryFn: () => api.documentAnalytics.getDocumentsPendingReview(filters),
    placeholderData: (prev) => prev,
  });
};

export const useDocumentVolumeOverTime = () => {
  const { filters } = useDocumentAnalyticsFilters();

  const days =
    filters.fromDate && filters.toDate
      ? differenceInDays(new Date(filters.fromDate), new Date(filters.toDate))
      : undefined;
  const params = {
    studyId: filters.studyId,
    sponsorId: filters.sponsorId,
    days: days || undefined,
  };

  return useQuery({
    queryKey: documentAnalyticsKeys.volumeOverTime(filters),
    queryFn: () => api.documentAnalytics.getDocumentVolumeOverTime(params),
    placeholderData: (prev) => prev,
  });
};

export const useUploadsByOriginationType = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.originationType(filters),
    queryFn: () => api.documentAnalytics.getUploadsByOriginationType(filters),
    placeholderData: (prev) => prev,
  });
};

export const useAllDocuments = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.allDocuments(filters),
    queryFn: () => api.documentAnalytics.getTotalDocumentsDetail(filters),
    placeholderData: (prev) => prev,
  });
};

export const usePendingDocuments = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.pendingDocuments(filters),
    queryFn: () =>
      api.documentAnalytics.getDocumentsPendingReviewDetail(filters),
    placeholderData: (prev) => prev,
  });
};

export const useSourceDocumentStatusFunnel = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.sourceDocumentStatusFunnel(filters),
    queryFn: () => api.documentAnalytics.getSourceDocumentStatusFunnel(filters),
    placeholderData: (prev) => prev,
  });
};

export const useISFArtifactStatusBreakdown = () => {
  const { filters } = useDocumentAnalyticsFilters();

  return useQuery({
    queryKey: documentAnalyticsKeys.isfArtifactStatusBreakdown(filters),
    queryFn: () => api.documentAnalytics.getISFArtifactStatusBreakdown(filters),
    placeholderData: (prev) => prev,
  });
};

export const useAgingDocuments = (page = 1) => {
  const [status] = useQueryState("status", parseAsString);
  const { filters } = useDocumentAnalyticsFilters();

  const params = {
    ...filters,
    status: status || undefined,
    page,
  };

  return useQuery({
    queryKey: documentAnalyticsKeys.agingDocuments(params),
    queryFn: () => api.documentAnalytics.getAgingDocuments(params),
    placeholderData: (prev) => prev,
  });
};

export const useSiteActivityLeaderboard = ({
  page = 1,
  search = "",
  sort,
}: {
  page?: number;
  search?: string;
  sort: SortingState;
}) => {
  const params = {
    ...sort,
    page,
    take: TAKE_50,
    filter: {
      nameFilter: search,
    },
    orderBy: sort[0]?.id ?? undefined,
    orderDirection: sort[0]?.desc ? "desc" : "asc",
  } as const;
  return useQuery({
    queryKey: documentAnalyticsKeys.sitePerformanceLeaderboard(params),
    queryFn: () => api.documentAnalytics.getSiteActivityLeaderboard(params),
    placeholderData: (prev) => prev,
  });
};

export const useInactiveSites = ({
  page = 1,
  search = "",
  sort,
}: {
  page?: number;
  search?: string;
  sort: SortingState;
}) => {
  const params = {
    page,
    take: TAKE_50,
    filter: {
      nameFilter: search,
    },
    orderBy: sort[0]?.id ?? undefined,
    orderDirection: sort[0]?.desc ? "desc" : "asc",
  } as const;

  return useQuery({
    queryKey: documentAnalyticsKeys.inactiveSites(params),
    queryFn: () => api.documentAnalytics.getInactiveSites(params),
    placeholderData: (prev) => prev,
  });
};
export const useSiteDocExchange = (page = 1, search = "") => {
  const params = {
    page,
    take: TAKE_50,
    filter: {
      nameFilter: search,
    },
  };
  return useQuery({
    queryKey: documentAnalyticsKeys.siteDocExchange(params),
    queryFn: () => api.documentAnalytics.getSiteDocExchange(params),
    placeholderData: (prev) => prev,
  });
};
