import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { ProtocolStudyArm } from "@/lib/apis/protocols/types";
import { cn } from "@/lib/utils";

import {
  useAddStudyArm,
  useUpdateStudyArm,
} from "./hooks/use-study-arms-mutations";

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedStudyArm: ProtocolStudyArm | null;
  protocolId: string;
};

export const StudyArmModal = function ({
  isOpen,
  onClose,
  selectedStudyArm,
  protocolId,
}: Props) {
  const { mutateAsync: addStudyArmMutate, isPending: isPendingAddStudyArm } =
    useAddStudyArm(protocolId);

  const {
    mutateAsync: updateStudyArmMutate,
    isPending: isPendingUpdateStudyArm,
  } = useUpdateStudyArm(protocolId);

  const isEditing = !!selectedStudyArm;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateStudyArmMutate({
          ...data,
          id: selectedStudyArm.id,
          protocolId,
        })
      : await addStudyArmMutate(data);

    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Study Arm`}
    >
      <Form
        defaultValues={{
          name: selectedStudyArm?.name || "",
          description: selectedStudyArm?.description || "",
          isActive:
            typeof selectedStudyArm?.isActive === "boolean"
              ? selectedStudyArm.isActive
              : true,
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter study arm name..."
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
              rows={4}
            />
          </div>
          <div
            className={cn("flex items-center gap-2", !isEditing && "hidden")}
          >
            <Checkbox id="isActive" name="isActive" />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isPendingAddStudyArm || isPendingUpdateStudyArm}
            isLoading={isPendingAddStudyArm || isPendingUpdateStudyArm}
            color="blue"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
