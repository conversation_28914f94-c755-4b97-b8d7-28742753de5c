"use client";

import { parseAsString, useQueryState } from "nuqs";
import { useEffect } from "react";
import { BiError } from "react-icons/bi";
import { FiDatabase } from "react-icons/fi";

import { BackButton } from "@/components/shared";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { useBreadcrumbs } from "@/hooks/use-breadcrumb";

import { HmacFailuresTable } from "./hmac-failures-table";
import { ProcessingErrorsTable } from "./processing-errors-table";

const SYSTEM_HEALTH_TABS = [
  {
    key: "processing-errors",
    title: (
      <p className="flex items-center gap-3">
        <BiError className="h-5 w-5 text-red-500" />
        System Processing Errors
      </p>
    ),
    content: <ProcessingErrorsTable />,
  },
  {
    key: "hmac-failures",
    title: (
      <p className="flex items-center gap-3">
        <FiDatabase className="h-5 w-5 text-orange-500" />
        HMAC Verification Failures
      </p>
    ),
    content: <HmacFailuresTable />,
  },
] as const;

const SELECT_OPTIONS = SYSTEM_HEALTH_TABS.map((tab) => ({
  label: tab.title,
  value: tab.key,
}));

export const SystemHealthContent = () => {
  const { setCustomBreadcrumbs } = useBreadcrumbs();
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);

  useEffect(() => {
    setCustomBreadcrumbs([
      {
        label: "System Health",
      },
    ]);
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <BackButton type="link" href="/dashboards?tab=system-health" />
        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
          System Vitals & Health
        </h1>
      </div>

      <div className="space-y-4 sm:hidden">
        <UncontrolledSelect
          placeholder="Select system health section"
          options={SELECT_OPTIONS}
          value={currentTab || ""}
          onChange={(value) => {
            if (value) {
              setCurrentTab(value);
            }
          }}
          className="w-full"
        />

        <div>
          {SYSTEM_HEALTH_TABS.find((tab) => tab.key === currentTab)?.content ??
            SYSTEM_HEALTH_TABS[0].content}
        </div>
      </div>

      <div className="hidden sm:block">
        <TabsWrapper tabs={SYSTEM_HEALTH_TABS} />
      </div>
    </div>
  );
};
