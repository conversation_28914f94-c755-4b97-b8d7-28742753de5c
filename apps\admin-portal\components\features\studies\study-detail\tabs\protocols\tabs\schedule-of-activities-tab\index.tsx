import { useMutation } from "@tanstack/react-query";
import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import toast from "react-hot-toast";
import { CiExport } from "react-icons/ci";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper.tsx";
import { Button } from "@/components/ui/button.tsx";
import { Table, TableLoading } from "@/components/ui/table";
import api from "@/lib/apis/index.ts";
import { Protocol, ProtocolEncounter } from "@/lib/apis/protocols/types.ts";
import { downloadBlob } from "@/lib/utils.ts";
import { useEncounterStore } from "@/stores/encounte-store.ts";

import { useProtocolEncounters } from "../../hooks/use-protocol-encounters.ts";
import { useProtocol } from "../../hooks/use-protocols-queries.ts";
import { useRemoveProtocolEncounter } from "../../hooks/use-remove-protocol-encounter.ts";
import { ModalAddEncounter } from "./modal-add-encounter.tsx";
import { ModalEncounterDetail } from "./modal-encounter-detail.tsx";
import { generateEncountersColumns } from "./visit-schedules.tsx";

type Props = {
  selectedProtocol: Protocol | null;
};

export const ScheduleOfActivitiesTab = ({ selectedProtocol }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isEncounterDetailOpen, setIsEncounterDetailOpen] = useState(false);
  const { setCurrentEncounter } = useEncounterStore();
  const { data: protocol } = useProtocol(selectedProtocol?.id);
  const { data: encounters, isPending } = useProtocolEncounters(
    selectedProtocol?.id as string,
  );

  const { mutateAsync: exportActivities, isPending: isExporting } = useMutation(
    {
      mutationFn: () =>
        api.protocols.exportScheduleOfActivity(selectedProtocol?.id || ""),
      onSettled: (_, err) =>
        !err && toast.success("Export schedule of activities successfully"),
      onError: (err) =>
        toast.error(err?.message || "Fail to export schedule of activities"),
    },
  );

  const { mutateAsync: removeEncounter, isPending: isDeleting } =
    useRemoveProtocolEncounter();

  const handleViewEncounterDetail = (encounter: ProtocolEncounter) => {
    setCurrentEncounter(encounter);
    setIsEncounterDetailOpen(true);
  };

  const handleDeleteEncounter = (protocolId: string, encounterId: string) => {
    if (isDeleting) return;
    removeEncounter({ protocolId, encounterId });
  };

  const columns = useMemo(
    () =>
      generateEncountersColumns(
        handleViewEncounterDetail,
        (encounterId: string) =>
          handleDeleteEncounter(selectedProtocol?.id as string, encounterId),
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [protocol],
  );

  const handleExport = async () => {
    const res = await exportActivities();
    if (!res) return;
    downloadBlob(res, "schedule-of-activities.csv");
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">
            Visit Schedule {selectedProtocol && `(${selectedProtocol.name})`}
          </div>
          <div className="flex items-center gap-2">
            <Button
              isLoading={isExporting}
              variant="primary"
              onClick={handleExport}
            >
              <CiExport />
              Export
            </Button>
            <Button
              disabled={protocol?.isPublished}
              variant="primary"
              onClick={() => setIsOpen(true)}
            >
              <IoMdAdd />
              Add Visit
            </Button>
          </div>
        </div>
        <ModalEncounterDetail
          isOpen={isEncounterDetailOpen}
          onClose={() => setIsEncounterDetailOpen(false)}
          selectedProtocol={selectedProtocol}
        />
        <ModalAddEncounter
          selectedProtocol={selectedProtocol}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isDeleting}>
            <Table columns={columns} data={encounters ?? []} />
          </LoadingWrapper>
        )}
      </Card>
    </>
  );
};
