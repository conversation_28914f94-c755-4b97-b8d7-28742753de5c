import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { auditLogColumns } from "./columns";
import { useCriticalEvents } from "./hooks/use-audit-log-queries";

export const CriticalEvents = () => {
  const { data, isPending, isPlaceholderData } = useCriticalEvents();

  return (
    <Card className="[&>div]:p-0">
      <h3 className="p-4 text-lg font-semibold text-gray-900 dark:text-white">
        Highlighted Critical Events
      </h3>

      {isPending ? (
        <TableLoading columns={auditLogColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={auditLogColumns} data={data?.results ?? []} />
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}
    </Card>
  );
};
