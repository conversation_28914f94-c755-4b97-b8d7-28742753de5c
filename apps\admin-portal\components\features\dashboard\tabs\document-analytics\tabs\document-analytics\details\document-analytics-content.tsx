"use client";

import { parseAsString, useQueryState } from "nuqs";
import { useEffect } from "react";
import { FiClock, FiFileText } from "react-icons/fi";

import { BackButton } from "@/components/shared";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { useBreadcrumbs } from "@/hooks/use-breadcrumb";

import { DocumentAnalyticsTable } from "./document-analytics-table";

const DOCUMENT_ANALYTICS_TABS = [
  {
    key: "all",
    title: (
      <p className="flex items-center gap-3">
        <FiFileText className="h-5 w-5 text-blue-500" />
        All Documents
      </p>
    ),
    content: <DocumentAnalyticsTable type="all" />,
  },
  {
    key: "pending",
    title: (
      <p className="flex items-center gap-3">
        <FiClock className="h-5 w-5 text-orange-500" />
        Pending Documents
      </p>
    ),
    content: <DocumentAnalyticsTable type="pending" />,
  },
] as const;

const SELECT_OPTIONS = DOCUMENT_ANALYTICS_TABS.map((tab) => ({
  label: tab.title,
  value: tab.key,
}));

export const DocumentAnalyticsContent = () => {
  const { setCustomBreadcrumbs } = useBreadcrumbs();
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);

  useEffect(() => {
    setCustomBreadcrumbs([
      {
        label: "Document Analytics",
      },
    ]);
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <BackButton type="link" href="/dashboards?tab=document-analytics" />
        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
          Document Analytics & Engagement
        </h1>
      </div>

      <div className="space-y-6">
        {/* Desktop Tabs */}
        <div className="hidden sm:block">
          <TabsWrapper tabs={DOCUMENT_ANALYTICS_TABS} />
        </div>

        {/* Mobile Select */}
        <div className="sm:hidden">
          <UncontrolledSelect
            options={SELECT_OPTIONS}
            value={currentTab ?? "all"}
            onChange={(value) => {
              if (value) {
                setCurrentTab(value);
              }
            }}
            placeholder="Select view"
          />

          <div className="mt-4">
            {DOCUMENT_ANALYTICS_TABS.find((tab) => tab.key === currentTab)
              ?.content ?? DOCUMENT_ANALYTICS_TABS[0].content}
          </div>
        </div>
      </div>
    </div>
  );
};
