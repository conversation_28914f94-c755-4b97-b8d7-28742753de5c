"use client";

import { Card } from "flowbite-react";
import { useRouter } from "next/navigation";
import path from "path";
import {
  FiClipboard,
  FiDatabase,
  FiFileText,
  FiMapPin,
  FiUsers,
} from "react-icons/fi";

import { Skeleton } from "@/components/ui/skeleton";
import { createRandomArray } from "@/lib/utils";

import { useOrphanedRecordStatistic } from "./hooks/use-data-integrity-queries";

const CARDS = [
  {
    label: "Studies No Protocol",
    icon: (
      <div
        className={`flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/40`}
      >
        <FiDatabase className={`size-7 text-blue-600 dark:text-blue-400`} />
      </div>
    ),
    color: "text-blue-600 dark:text-blue-400",
    key: "activeStudiesNoProtocol",
    path: "studies-no-protocol",
  },
  {
    label: "Sites No Assigned User",
    icon: (
      <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/40">
        <FiMapPin className="size-7 text-green-600 dark:text-green-400" />
      </div>
    ),
    color: "text-green-600 dark:text-green-400",
    key: "activeSitesNoAssignedUser",
    path: "sites-no-assigned-user",
  },
  {
    label: "Protocols No Study",
    icon: (
      <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/40">
        <FiFileText className="size-7 text-purple-600 dark:text-purple-400" />
      </div>
    ),
    color: "text-purple-600 dark:text-purple-400",
    key: "publishedProtocolsNoStudy",
    path: "protocols-no-study",
  },
  {
    label: "Patients Inactive Study/Site",
    icon: (
      <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/40">
        <FiUsers className="size-7 text-orange-600 dark:text-orange-400" />
      </div>
    ),
    color: "text-orange-600 dark:text-orange-400",
    key: "patientsAssignedInactiveStudyOrSite",
    path: "patients-inactive-study-site",
  },
  {
    label: "Tasks Inactive Users/Groups",
    icon: (
      <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/40">
        <FiClipboard className="size-7 text-red-600 dark:text-red-400" />
      </div>
    ),
    color: "text-red-600 dark:text-red-400",
    key: "tasksAssignInactiveUsersOrGroups",
    path: "tasks-inactive-users-groups",
  },
] as const;

export const OrphanedRecordsMonitor = () => {
  const router = useRouter();
  const { data: orphanedRecords, isPending: isPendingOrphanedRecords } =
    useOrphanedRecordStatistic();

  const handleCardClick = (path: string) => {
    router.push(`/dashboards/configurations?tab=${path}`);
  };

  if (isPendingOrphanedRecords) {
    return <OrphanedRecordsSkeleton />;
  }

  return (
    <>
      <div>
        <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
          Orphaned Records Monitor
        </h3>
        <div className="grid gap-4 sm:grid-cols-[repeat(auto-fit,minmax(400px,1fr))] ">
          {CARDS.map((card) => {
            return (
              <Card
                key={card.key}
                className="cursor-pointer transition-shadow hover:shadow-lg dark:hover:bg-slate-700"
                onClick={() => handleCardClick(card.path)}
              >
                <div className="flex items-center gap-4">
                  {card.icon}
                  <div className="flex-1">
                    <span className={`text-2xl font-bold ${card.color}`}>
                      {orphanedRecords?.[card.key]}
                    </span>
                    <span className="mt-1 block text-base font-medium text-gray-900 dark:text-white">
                      {card.label}
                    </span>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    </>
  );
};

const OrphanedRecordsSkeleton = () => (
  <div>
    <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
      Orphaned Records Monitor
    </h3>
    <div className="grid gap-4 sm:grid-cols-[repeat(auto-fit,minmax(400px,1fr))]">
      {createRandomArray(5).map((index) => (
        <Card key={index} className="[&>div]:p-6">
          <div className="flex items-center gap-4">
            <Skeleton className="size-14 flex-shrink-0 rounded-full" />
            <div className="flex-1">
              <Skeleton className="mb-1 h-[29px] w-16" />
              <Skeleton className="h-[24px] w-24" />
            </div>
          </div>
        </Card>
      ))}
    </div>
  </div>
);
