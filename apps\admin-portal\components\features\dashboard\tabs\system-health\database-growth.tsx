"use client";

import { Card, useThemeMode } from "flowbite-react";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { FiDatabase } from "react-icons/fi";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Skeleton } from "@/components/ui/skeleton";
import { DatabaseGrowthResponse } from "@/lib/apis/system-health";
import { snakeCaseToCapitalized } from "@/utils/string";

import {
  calculateDateRange,
  DateOptions,
  DateRangeFilter,
} from "../components";
import { PeriodToggle } from "../components/period-toggle";
import { useDatabaseGrowth } from "./hooks/use-system-health-queries";

const CHART_COLORS = [
  "#3b82f6", // blue-500
  "#10b981", // emerald-500
  "#f59e0b", // amber-500
  "#ef4444", // red-500
  "#8b5cf6", // violet-500
  "#06b6d4", // cyan-500
  "#84cc16", // lime-500
  "#f97316", // orange-500
];

const transformDataForChart = (apiData?: DatabaseGrowthResponse) => {
  if (!apiData?.tables?.length) return [];

  const allDates = new Set<string>();
  apiData.tables.forEach((table) => {
    table.data.forEach((point) => {
      allDates.add(point.date);
    });
  });

  const sortedDates = Array.from(allDates).sort();

  return sortedDates.map((date) => {
    const dataPoint: Record<string, number | string> = { date };

    apiData.tables.forEach((table) => {
      const tableData = table.data.find((point) => point.date === date);
      dataPoint[table.table] = tableData?.count || 0;
    });

    return dataPoint;
  });
};

export const DatabaseGrowth = () => {
  const { mode } = useThemeMode();
  const [unit, setUnit] = useState<"daily" | "weekly">("daily");
  const [selectedDateFilter, setSelectedDateFilter] = useState<
    DateOptions | ""
  >("last_month");
  const [selectedRange, setSelectedRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  const handleFilterChange = (
    filter: DateOptions | "",
    customRange?: DateRange,
  ) => {
    setSelectedDateFilter(filter);
    if (customRange) {
      setSelectedRange(customRange);
    }
  };

  const dateRange = calculateDateRange(selectedDateFilter, selectedRange);

  const { data, isPending } = useDatabaseGrowth({
    fromDate: dateRange?.fromDate,
    toDate: dateRange?.toDate,
    unit: unit === "daily" ? "day" : "week",
  });

  if (isPending) {
    return <DatabaseGrowthSkeleton />;
  }

  const chartData = transformDataForChart(data);

  return (
    <Card>
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Database Growth Over Time
        </h3>
        <FiDatabase className="h-6 w-6 text-blue-500" />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-end gap-4">
          <PeriodToggle currentPeriod={unit} setCurrentPeriod={setUnit} />
          <DateRangeFilter
            selectedFilter={selectedDateFilter}
            onFilterChange={handleFilterChange}
            selectedRange={selectedRange}
          />
        </div>

        <div className="h-80">
          {!chartData?.length ? (
            <div className=" grid h-full place-content-center gap-2 text-center text-gray-500 dark:text-gray-400">
              <FiDatabase className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
              <p className="mt-2 text-sm">No database growth data available</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tick={{
                    fontSize: 12,
                  }}
                  stroke={mode === "dark" ? "#fff" : "#000"}
                  interval="preserveStartEnd"
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                />
                <YAxis
                  tick={{
                    fontSize: 12,
                  }}
                  stroke={mode === "dark" ? "#fff" : "#000"}
                  allowDecimals={false}
                  tickFormatter={(value) => value.toLocaleString()}
                />
                <Tooltip
                  isAnimationActive={false}
                  labelFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    });
                  }}
                  formatter={(value, name) => [
                    `${Number(value).toLocaleString()} records`,
                    snakeCaseToCapitalized(name as string),
                  ]}
                />
                <Legend />
                {data?.tables.map((table, index) => (
                  <Line
                    key={table.table}
                    type="monotone"
                    dataKey={table.table}
                    name={snakeCaseToCapitalized(table.table)}
                    stroke={CHART_COLORS[index % CHART_COLORS.length]}
                    strokeWidth={2}
                    dot={{
                      fill: CHART_COLORS[index % CHART_COLORS.length],
                      strokeWidth: 2,
                      r: 4,
                    }}
                    activeDot={{
                      r: 6,
                      stroke: CHART_COLORS[index % CHART_COLORS.length],
                      strokeWidth: 2,
                    }}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </div>
    </Card>
  );
};

export const DatabaseGrowthSkeleton = () => {
  return (
    <Card>
      <div className="mb-6 flex items-center justify-between">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-6 rounded-full" />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-end gap-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-8" />
            <Skeleton className="h-6 w-10 rounded-full" />
            <Skeleton className="h-4 w-12" />
          </div>
          <Skeleton className="h-8 w-48" />
        </div>

        <Skeleton className="h-80 w-full rounded-lg" />
      </div>
    </Card>
  );
};
