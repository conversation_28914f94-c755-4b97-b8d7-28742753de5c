import { useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddStudyContactPayload } from "@/lib/apis/studies";

import { contactKeys } from "./use-contacts-queries";

export const useAddStudyContact = () => {
  const params = useParams();
  const studyId = params.id as string;

  return useMutation({
    mutationFn: (data: AddStudyContactPayload & { studyId: string }) => {
      const { studyId: dataStudyId, ...rest } = data;
      return api.studies.addContact(dataStudyId, rest);
    },
    onError: (error) => toast.error(error.message || "Failed to add contact"),
    onSettled: (_, err) => !err && toast.success("Contact added successfully"),
    meta: {
      awaits: contactKeys.allLists(studyId),
    },
  });
};
