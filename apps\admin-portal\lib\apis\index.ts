import { activities } from "./activities";
import { addresses } from "./addresses";
import { promptTemplate } from "./app-prompts";
import { artifactCategories } from "./artifact-categories";
import { artifactStatus } from "./artifact-status";
import { assignments } from "./assignments";
import { auditLog } from "./audit-log";
import { auth } from "./auth";
import { categorizationAnalyticsApi } from "./categorization-analytics";
import { categoryAiSummary } from "./category-ai-summary";
import { cohorts } from "./cohorts";
import { contacts } from "./contacts";
import { dataIntegrity } from "./data-integrity";
import { docExchange } from "./doc-exchange";
import { documentAnalytics } from "./document-analytics";
import { encounters } from "./encounters";
import { epochs } from "./epochs";
import { essentialDocumentFiles } from "./essential-document-files";
import { essentialDocumentVersions } from "./essential-document-versions";
import { essentialDocuments } from "./essential-documents";
import { groups } from "./groups";
import { isfFolders } from "./isf-folders";
import { isfRefModelApi } from "./isf-ref-models";
import { isfTemplates } from "./isf-templates";
import { patientVisits } from "./patient-visits";
import { patients } from "./patients";
import { procedures } from "./procedures";
import { promptVariables } from "./prompt-variables";
import { protocolVisits } from "./protocol-visits";
import { protocols } from "./protocols";
import { IsfTemplateProtocolApi } from "./protocols/isf-template-protocols";
import { roles } from "./roles";
import { scannerApplications } from "./scanner-applications";
import { scannerModels } from "./scanner-models";
import { scanners } from "./scanners";
import { sites } from "./sites";
import { sponsors } from "./sponsors";
import { studies } from "./studies";
import { studyArms } from "./study-arms";
import { systemHealth } from "./system-health";
import { tasks } from "./tasks";
import { tmfRefModelApi } from "./tmf-ref-models";
import { trainingModules } from "./training-modules";
import { userManagement } from "./user-management";
import { users } from "./users";
import { visitDocuments } from "./visit-documents";
import { visitSchedules } from "./visit-schedules";
import { visitTypes } from "./visit-types";
import { visits } from "./visits";

const api = {
  addresses,
  auditLog,
  auth,
  categorizationAnalyticsApi,
  scanners,
  scannerModels,
  scannerApplications,
  artifactCategories,
  sponsors,
  studies,
  sites,
  users,
  groups,
  contacts,
  protocols,
  visitSchedules,
  assignments,
  IsfTemplateProtocolApi,
  visits,
  protocolVisits,
  visitTypes,
  encounters,
  activities,
  epochs,
  studyArms,
  procedures,
  cohorts,
  essentialDocumentFiles,
  isfFolders,
  isfTemplates,
  roles,
  promptTemplate,
  trainingModules,
  tmfRefModelApi,
  isfRefModelApi,
  promptVariables,
  essentialDocumentVersions,
  categoryAiSummary,
  artifactStatus,
  essentialDocuments,
  tasks,
  dataIntegrity,
  docExchange,
  documentAnalytics,
  patients,
  patientVisits,
  visitDocuments,
  userManagement,
  systemHealth,
};

export default api;
