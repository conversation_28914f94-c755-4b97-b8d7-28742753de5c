import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

import { useAutoCategorizationFilters } from "./use-auto-categorization-filters";

export const autoCategoryKeys = {
  all: () => ["auto-categorization-list"] as const,

  allLists: () => [...autoCategoryKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...autoCategoryKeys.allLists(), params] as const,
};

export const useAutoCategorization = () => {
  const { params } = useAutoCategorizationFilters();

  return useQuery({
    queryKey: autoCategoryKeys.list(params),
    queryFn: () => api.categorizationAnalyticsApi.getDrillDownTable(params),
    placeholderData: (prev) => prev,
  });
};
