import { ToggleSwitch, ToggleSwitchProps } from "flowbite-react";
import React from "react";
import { Controller, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

type Props = Omit<ToggleSwitchProps, "checked" | "onChange"> & {
  name: string;
  shouldShowError?: boolean;
};

export const Switch = ({ name, shouldShowError = true, ...props }: Props) => {
  const { control } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, ...rest }, formState: { errors } }) => {
        const errorMessage = errors[name]?.message?.valueOf();
        const hasError = typeof errorMessage === "string";

        return (
          <>
            <ToggleSwitch
              {...rest}
              checked={value}
              className={cn(props.className)}
              {...props}
            />
            {hasError && shouldShowError && (
              <span className="text-sm text-red-500">{errorMessage}</span>
            )}
          </>
        );
      }}
    />
  );
};
