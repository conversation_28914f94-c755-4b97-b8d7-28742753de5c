"use client";

import { Card } from "flowbite-react";
import { useEffect } from "react";

import { BackButton } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { useBreadcrumbs } from "@/hooks/use-breadcrumb";

import { AutoCategorizationTableFilter } from "./auto-categorization-table-filter";
import { columns } from "./columns";
import { useAutoCategorization } from "./hooks/use-auto-categorization-queries";

export const AutoCategorizationDetail = () => {
  const { setBreadcrumbs } = useBreadcrumbs();
  const { data, isPending, isPlaceholderData } = useAutoCategorization();

  useEffect(() => {
    setBreadcrumbs([{ label: "Auto Categorization" }]);
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <BackButton
          type="link"
          href="/dashboards?tab=analytics&subTab=categorization"
        />
        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
          Auto Categorization
        </h1>
      </div>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-end p-4">
          <AutoCategorizationTableFilter />
        </div>

        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} enableSorting data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
