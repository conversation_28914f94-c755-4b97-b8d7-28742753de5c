import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";
import { MILLISECONDS_IN_MINUTE } from "@/lib/constants";

import { useAuditLogFilters } from "./use-audit-log-filters";

export const auditLogKeys = {
  all: () => ["audit-log"] as const,

  allActivityFeeds: () => [...auditLogKeys.all(), "activity-feed"] as const,
  activityFeed: (params?: MetadataParams) =>
    [...auditLogKeys.allActivityFeeds(), params] as const,

  allCriticalEvents: () => [...auditLogKeys.all(), "critical-events"] as const,
  criticalEvents: (params?: MetadataParams) =>
    [...auditLogKeys.allCriticalEvents(), params] as const,

  allActions: () => [...auditLogKeys.all(), "actions"] as const,
  allResourceTypes: () => [...auditLogKeys.all(), "resource-types"] as const,
};

export const useActivityFeed = () => {
  const { params } = useAuditLogFilters();

  return useQuery({
    queryKey: auditLogKeys.activityFeed(params),
    queryFn: () => api.auditLog.getActivityLog(params),
    placeholderData: (prev) => prev,
  });
};

export const useCriticalEvents = () => {
  const { page, take } = usePagination();

  const params = {
    page,
    take,
  };

  return useQuery({
    queryKey: auditLogKeys.criticalEvents(params),
    queryFn: () => api.auditLog.getCriticalEvents(params),
    placeholderData: (prev) => prev,
  });
};

export const useAuditLogActions = () => {
  return useQuery({
    queryKey: auditLogKeys.allActions(),
    queryFn: () => api.auditLog.getAllActions(),
    staleTime: MILLISECONDS_IN_MINUTE * 30,
  });
};

export const useAuditLogResourceTypes = () => {
  return useQuery({
    queryKey: auditLogKeys.allResourceTypes(),
    queryFn: () => api.auditLog.getAllResourceTypes(),
    staleTime: MILLISECONDS_IN_MINUTE * 30,
  });
};
