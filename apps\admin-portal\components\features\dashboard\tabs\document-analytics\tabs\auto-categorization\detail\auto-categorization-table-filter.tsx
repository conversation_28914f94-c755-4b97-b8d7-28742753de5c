"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { useSiteStudies } from "@/components/features/sites/site-detail/tabs/hooks/use-site-studies";
import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { DateRangePicker, Form, FormRef, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelectV2 } from "@/components/ui/lazy-select/lazy-select-v2";
import { useInfiniteSites } from "@/hooks/queries/use-infinite-sites";
import { cn } from "@/lib/utils";

import { useAutoCategorizationFilters } from "./hooks/use-auto-categorization-filters";

export const AUTO_CATEGORIZATION_STATUS = [
  { label: "Success", value: "success" },
  { label: "Failed", value: "failed" },
  { label: "Manual Override", value: "manual_override" },
] as const;

export const AUTO_CATEGORIZATION_METHODS = [
  { label: "Auto", value: "auto" },
  { label: "Manual", value: "manual" },
  { label: "Auto To Manual", value: "auto_to_manual" },
  { label: "AI Failed Manual", value: "ai_failed_manual" },
] as const;

const schema = z.object({
  siteId: z.string().optional().nullable(),
  studyId: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  method: z.string().optional().nullable(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const AutoCategorizationTableFilter = () => {
  const [open, setOpen] = useState(false);

  const {
    siteId,
    setSiteId,
    studyId,
    setStudyId,
    status,
    setStatus,
    method,
    setMethod,
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    goToPage,
  } = useAutoCategorizationFilters();

  const [selectedSiteId, setSelectedSiteId] = useState(siteId || "");
  const { data: studies } = useSiteStudies(selectedSiteId);

  const formRef = useRef<FormRef<typeof schema>>(null);

  const handleApplyFilters = (values: z.infer<typeof schema>) => {
    setSiteId(values.siteId || null);
    setStudyId(values.studyId || null);
    setStatus(values.status || null);
    setMethod(values.method || null);
    setFromDate(values.dateRange?.from?.toISOString() || null);
    setToDate(values.dateRange?.to?.toISOString() || null);
    setOpen(false);
    goToPage(1);
  };

  const handleClearFilters = () => {
    setSiteId(null);
    setStudyId(null);
    setStatus(null);
    setMethod(null);
    setFromDate(null);
    setToDate(null);
    formRef.current?.formHandler.reset();
    setSelectedSiteId("");
    setOpen(false);
    goToPage(1);
  };

  const activeFilterCount = [
    siteId,
    studyId,
    status,
    method,
    fromDate || toDate,
  ].filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen}>
      <DropdownTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "relative flex items-center gap-2 px-3 py-2",
            "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:bg-gray-50",
            "dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:border-gray-500 dark:hover:bg-gray-700",
          )}
        >
          <ListFilter className="h-4 w-4" />
          {activeFilterCount > 0 && (
            <span className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs font-medium text-white">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>

      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <Form
          schema={schema}
          mode="onChange"
          onSubmit={handleApplyFilters}
          className="w-80  sm:w-96"
          ref={formRef}
          defaultValues={{
            siteId: siteId || "",
            studyId: studyId || "",
            status: status || "",
            method: method || "",
            dateRange: {
              from: fromDate ? new Date(fromDate) : undefined,
              to: toDate ? new Date(toDate) : undefined,
            },
          }}
        >
          <div className="flex flex-col divide-y dark:divide-gray-500">
            <div className="space-y-1 px-4 py-2">
              <Label
                htmlFor="siteId"
                className=" block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Site
              </Label>
              <LazySelectV2
                name="siteId"
                useInfiniteQuery={useInfiniteSites}
                getOptionLabel={(site) => site.name}
                getOptionValue={(site) => site.id}
                placeholder="Select site"
                searchPlaceholder="Search sites..."
                onSelect={(value) => {
                  setSelectedSiteId(value);
                }}
              />
            </div>

            <div className="space-y-1 px-4 py-2">
              <Label
                htmlFor="studyId"
                className=" block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Study
              </Label>
              <Select
                name="studyId"
                options={studies?.results.map((study) => ({
                  label: study.study.name,
                  value: study.studyId,
                }))}
              />
            </div>

            <div className="space-y-1 px-4 py-2">
              <Label
                htmlFor="status"
                className=" block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Status
              </Label>
              <Select
                name="status"
                options={AUTO_CATEGORIZATION_STATUS}
                placeholder="Select status"
              />
            </div>

            <div className="space-y-1 px-4 py-2">
              <Label
                htmlFor="method"
                className=" block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Method
              </Label>
              <Select
                name="method"
                options={AUTO_CATEGORIZATION_METHODS}
                placeholder="Select method"
              />
            </div>

            <div className="space-y-1 px-4 py-2">
              <Label
                htmlFor="dateRange"
                className=" block text-sm font-normal leading-6 text-gray-700 sm:text-base dark:text-gray-300"
              >
                Date Range
              </Label>
              <DateRangePicker
                name="dateRange"
                placeholder="Select date range"
                placement="bottom-end"
                maxDate={new Date()}
              />
            </div>
          </div>

          <div className="flex justify-end gap-5 px-4 py-2">
            <Button
              variant="outline"
              className={cn(
                "w-full justify-center",
                activeFilterCount === 0 && "pointer-events-none invisible",
              )}
              onClick={handleClearFilters}
              type="button"
            >
              Clear Filters
            </Button>
            <Button
              variant="primary"
              type="submit"
              className="w-full justify-center"
            >
              Apply Filters
            </Button>
          </div>
        </Form>
      </DropdownContent>
    </Dropdown>
  );
};
