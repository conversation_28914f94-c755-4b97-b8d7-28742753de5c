import { useParams } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Checkbox, Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import type { Procedure } from "@/lib/apis/procedures";
import { cn } from "@/lib/utils";

import {
  useAddProcedure,
  useEditProcedure,
} from "./hooks/use-procedures-mutations";

export const procedureSchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type ModalProcedureProps = {
  isOpen: boolean;
  onClose: () => void;
  procedure?: Procedure | null;
};

export const ModalProcedure = ({
  isOpen,
  onClose,
  procedure,
}: ModalProcedureProps) => {
  const params = useParams();
  const studyId = params?.id as string;
  const { mutateAsync: addProcedure, isPending: isAdding } =
    useAddProcedure(studyId);
  const { mutateAsync: editProcedure, isPending: isEditing } =
    useEditProcedure(studyId);

  const isEditingMode = Boolean(procedure);

  async function onSubmit(data: z.infer<typeof procedureSchema>) {
    if (isEditingMode && procedure) {
      await editProcedure({
        id: procedure.id,
        studyId: params?.id as string,
        ...data,
      });
    } else {
      await addProcedure({ ...data, studyId: params?.id as string });
    }
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>
        {isEditingMode ? "Edit Procedure" : "Add Procedure"}
      </Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={procedureSchema}
          onSubmit={onSubmit}
          defaultValues={{
            name: procedure?.name || "",
            description: procedure?.description || "",
            isActive: isEditingMode ? procedure?.isActive : true,
          }}
        >
          <ProcedureForm
            onClose={onClose}
            isSubmitting={isAdding || isEditing}
            isEditing={isEditingMode}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type ProcedureFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
  isEditing: boolean;
};

const ProcedureForm = ({
  onClose,
  isSubmitting,
  isEditing,
}: ProcedureFormProps) => {
  return (
    <div className="grid grid-cols-1 gap-3 sm:gap-6">
      <div className="space-y-1">
        <Label htmlFor="name">Name</Label>
        <InputField id="name" name="name" placeholder="Enter name..." />
      </div>
      <div className="space-y-1">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          placeholder="Enter description..."
        />
      </div>
      <div className={cn("flex items-center gap-2", !isEditing && "hidden")}>
        <Checkbox id="isActive" name="isActive" />
        <Label htmlFor="isActive">Active</Label>
      </div>
      <div className="flex flex-col justify-end gap-4 border-none pt-0 sm:flex-row sm:gap-5 ">
        <CloseButton onClose={onClose} />
        <Button type="submit" color="blue" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </div>
  );
};
