import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";

import { USE_PROTOCOL_ENCOUNTERS_QUERY_KEY } from "./use-protocol-encounters";

export const useRemoveProtocolEncounter = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      protocolId,
      encounterId,
    }: {
      protocolId: string;
      encounterId: string;
    }) => api.protocols.archiveProtocolEncounter(protocolId, encounterId),
    onSuccess: (_, { protocolId }) => {
      queryClient.invalidateQueries({
        queryKey: [USE_PROTOCOL_ENCOUNTERS_QUERY_KEY, protocolId],
      });
      toast.success("Delete visit successfully");
    },
  });
};
