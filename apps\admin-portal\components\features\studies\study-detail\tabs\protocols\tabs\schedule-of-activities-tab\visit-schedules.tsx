import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { IoMdEye } from "react-icons/io";
import { MdDelete } from "react-icons/md";

import { ProtocolEncounter } from "@/lib/apis/protocols/types";

export const generateEncountersColumns = (
  handleView: (encounter: ProtocolEncounter) => void,
  handleDelete: (encounterId: string) => void,
) => {
  const columns: ColumnDef<ProtocolEncounter>[] = [
    {
      header: "Visit Name",
      accessorKey: "name",
    },
    {
      header: "Visit Type",
      accessorKey: "visitType.name",
    },
    {
      header: "Study Arm",
      accessorKey: "studyArm.name",
    },
    {
      header: "Visit Day",
      cell: ({ row }) => {
        const { visitDay, visitWindowStart, visitWindowEnd } = row.original;
        if (typeof visitDay === "number") {
          let windowText = "";

          if (
            typeof visitWindowStart === "number" &&
            typeof visitWindowEnd === "number"
          ) {
            windowText = ` (${visitWindowStart}/${visitWindowEnd > 0 ? "+" : ""}${visitWindowEnd})`;
          }

          return (
            <div className="overflow-hidden text-ellipsis whitespace-nowrap">
              <span>
                Day {visitDay}
                {windowText}
              </span>
            </div>
          );
        }
      },
    },
    {
      header: "Total Activities",
      accessorKey: "totalActivities",
    },
    {
      header: "Actions",
      cell: ({ row }) => {
        return (
          <div className="flex gap-3">
            {/* View */}
            <Link
              onClick={() => handleView(row.original)}
              className="text-destructive flex items-center gap-1 text-xs text-blue-500"
              href={""}
            >
              <IoMdEye />
              <span>View</span>
            </Link>
            {/* Delete */}
            <button
              className="flex items-center gap-1 text-xs text-red-500 hover:text-red-600 disabled:cursor-not-allowed disabled:text-gray-300"
              onClick={() => handleDelete(row.original.id as string)}
            >
              <MdDelete className="h-4 w-4" />
              Delete
            </button>
          </div>
        );
      },
    },
  ];

  return columns;
};
