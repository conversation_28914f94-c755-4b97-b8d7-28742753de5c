import { AUTO_CATEGORIZATION_METHODS } from "@/components/features/dashboard/tabs/document-analytics/tabs/auto-categorization/detail/auto-categorization-table-filter";
import { MetadataParams } from "@/lib/apis/types";

import { ListBaseResponse } from "../types";

export type Method = (typeof AUTO_CATEGORIZATION_METHODS)[number]["value"];

export type MethodDistributionResponse = {
  method: Method;
  count: number;
};

export type MethodTrend = {
  date: string;
  method: Method;
  count: number;
};

export type MethodTrendsResponse = {
  data: MethodTrend[];
  granularity: "day" | "week";
};

export type AutoCategorizationParams = {
  fromDate?: string;
  toDate?: string;
  siteId?: string;
  studyId?: string;
  granularity?: "day" | "week";
};

export type CategorizationTrendData = {
  date: string;
  AI: number;
  Manual: number;
  "Auto-to-Manual": number;
  "AI Failed Manual": number;
};

export type CategorizationTrendsResponse = {
  data: CategorizationTrendData[];
  totalDocuments: number;
  dateRange: {
    start: string;
    end: string;
  };
};

export type AutoCategorizationRecord = {
  fileName: string;
  study: string;
  site: string;
  method: string;
  status: "failed" | "success" | "manual_override" | null;
  confidenceScore: number;
  dateTime: string;
  reason?: string;
};

export type AiSuccessRateResponse = {
  aiSuccessRate: number;
  aiAccuracy: number;
  autoCount: number;
  manualCount: number;
};

export type AiFailureRateResponse = {
  rate: number;
  methodCount: number;
  totalCount: number;
};

export type ConversionRateResponse = {
  rate: number;
  methodCount: number;
  totalCount: number;
};

export type AiSuccessRateTrendResponse = {
  date: string;
  aiSuccessRate: number;
}[];
