import type { ClassValue } from "clsx";
import clsx from "clsx";
import { format } from "date-fns";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date | string, template = "yyyy-MM-dd HH:mm") {
  return format(date, template);
}

export async function generateHmac(
  file: File,
  hmacKey: string,
): Promise<string> {
  const keyData = new TextEncoder().encode(hmacKey);
  const cryptoKey = await window.crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"],
  );

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (event) => {
      const fileData = event.target?.result as ArrayBuffer;
      const signature = await window.crypto.subtle.sign(
        "HMAC",
        cryptoKey,
        fileData,
      );
      const hmac = Array.from(new Uint8Array(signature))
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("");
      resolve(hmac);
    };
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
}

export const sleep = (ms = 1000) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const createRandomArray = (amount = 3) => {
  return Array(amount)
    .fill(0)
    .map((_, i) => i);
};

export const downloadBlob = (blob: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(new Blob([blob]));

  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;

  document.body.appendChild(a);
  a.click();

  a.remove();
  window.URL.revokeObjectURL(url);
};

export const generateEmptyCsv = (headers: string[]) => {
  return headers.join(",") + "\n";
};

export const kebabCaseToCapitalize = (str: string) => {
  return str
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
