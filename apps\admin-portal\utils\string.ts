export const snakeCaseToCapitalized = (text: string): string => {
  if (!text || typeof text !== "string") {
    return "";
  }

  return text
    .split("_")
    .map((word) => {
      if (word.length === 0) return "";
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(" ");
};

export const capitalize = (str?: string) => {
  if (!str) return "";
  return str
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
