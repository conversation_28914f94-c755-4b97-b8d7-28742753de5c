import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { PropsWithChildren } from "react";

import { cn } from "@/lib/utils";
import { snakeCaseToCapitalized } from "@/utils/string";

import { PillBadge } from "./pill-badge";

const activityBadgeVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap relative",
  {
    variants: {
      variant: {
        // Document & Version Lifecycle
        DOCUMENT_CREATED:
          "bg-emerald-50 text-emerald-600 before:bg-emerald-600",
        DOCUMENT_VIEWED: "bg-blue-50 text-blue-600 before:bg-blue-600",
        DOCUMENT_DOWNLOADED:
          "bg-indigo-50 text-indigo-600 before:bg-indigo-600",
        METADATA_UPDATED: "bg-amber-50 text-amber-600 before:bg-amber-600",
        VERSION_UPLOADED: "bg-purple-50 text-purple-600 before:bg-purple-600",
        DOCUMENT_DELETED: "bg-red-50 text-red-600 before:bg-red-600",
        DOCUMENT_RESTORED: "bg-green-50 text-green-600 before:bg-green-600",
        DOCUMENT_LISTED: "bg-slate-50 text-slate-600 before:bg-slate-600",
        DOCUMENT_WITHDRAWN: "bg-orange-50 text-orange-600 before:bg-orange-600",

        // Folders lifecycle
        FOLDER_CREATED: "bg-teal-50 text-teal-600 before:bg-teal-600",
        FOLDER_VIEWED: "bg-cyan-50 text-cyan-600 before:bg-cyan-600",
        FOLDER_DELETED: "bg-rose-50 text-rose-600 before:bg-rose-600",
        FOLDER_UPDATED: "bg-yellow-50 text-yellow-600 before:bg-yellow-600",

        // Consent lifecycle
        CONSENT_CREATED: "bg-violet-50 text-violet-600 before:bg-violet-600",
        CONSENT_VIEWED: "bg-sky-50 text-sky-600 before:bg-sky-600",
        CONSENT_UPDATED: "bg-lime-50 text-lime-600 before:bg-lime-600",
        CONSENT_LISTED: "bg-gray-50 text-gray-600 before:bg-gray-600",

        // Medical history
        MEDICAL_HISTORY_VIEWED: "bg-pink-50 text-pink-600 before:bg-pink-600",
        MEDICAL_HISTORY_CREATED:
          "bg-fuchsia-50 text-fuchsia-600 before:bg-fuchsia-600",

        // Patient
        PATIENT_CREATED: "bg-emerald-50 text-emerald-700 before:bg-emerald-700",
        PATIENT_LISTED: "bg-stone-50 text-stone-600 before:bg-stone-600",
        PATIENT_DELETED: "bg-red-50 text-red-700 before:bg-red-700",
        PATIENT_VIEWED: "bg-blue-50 text-blue-700 before:bg-blue-700",
        PATIENT_UPDATED: "bg-orange-50 text-orange-700 before:bg-orange-700",

        // Studies
        STUDIES_LISTED: "bg-neutral-50 text-neutral-600 before:bg-neutral-600",
        STUDIES_VIEWED: "bg-sky-50 text-sky-700 before:bg-sky-700",
        STUDIES_CREATED: "bg-green-50 text-green-700 before:bg-green-700",
        STUDIES_UPDATED: "bg-amber-50 text-amber-700 before:bg-amber-700",

        // Workflow & Status Transitions
        STATUS_CHANGED: "bg-indigo-50 text-indigo-700 before:bg-indigo-700",

        // Collaboration & Review
        COMMENT_ADDED: "bg-blue-50 text-blue-500 before:bg-blue-500",
        COMMENT_EDITED: "bg-yellow-50 text-yellow-600 before:bg-yellow-600",
        COMMENT_DELETED: "bg-red-50 text-red-500 before:bg-red-500",
        ANNOTATION_ADDED: "bg-purple-50 text-purple-500 before:bg-purple-500",
        REDACTION_ADDED: "bg-gray-50 text-gray-700 before:bg-gray-700",

        // Compliance & Electronic Signatures
        SIGNATURE_APPLIED: "bg-green-50 text-green-700 before:bg-green-700",
        SIGNATURE_ADDED: "bg-emerald-50 text-emerald-700 before:bg-emerald-700",
        SIGNATURE_REVOKED: "bg-red-50 text-red-700 before:bg-red-700",
        AUDIT_TRAIL_EXPORTED: "bg-slate-50 text-slate-700 before:bg-slate-700",

        // Access Control & Permissions
        USER_ACCESS_GRANTED: "bg-green-50 text-green-600 before:bg-green-600",
        USER_ACCESS_REVOKED: "bg-red-50 text-red-600 before:bg-red-600",
        DOCUMENT_SHARED: "bg-blue-50 text-blue-600 before:bg-blue-600",

        // System & Automated Actions
        DOCUMENT_ARCHIVED: "bg-gray-50 text-gray-600 before:bg-gray-600",
        DOCUMENT_CLASSIFIED:
          "bg-violet-50 text-violet-600 before:bg-violet-600",
        LOGIN_SUCCESS: "bg-green-50 text-green-500 before:bg-green-500",

        create:
          "bg-green-50 text-green-400 before:bg-green-400 dark:bg-green-900/20 dark:text-green-300 dark:before:bg-green-300",
        update:
          "bg-orange-50 text-orange-400 before:bg-orange-400 dark:bg-orange-900/20 dark:text-orange-300 dark:before:bg-orange-300",
        read: "bg-blue-50 text-blue-500 before:bg-blue-500 dark:bg-blue-900/20 dark:text-blue-400 dark:before:bg-blue-400",
        delete:
          "bg-red-50 text-red-400 before:bg-red-400 dark:bg-red-900/20 dark:text-red-300 dark:before:bg-red-300",
      },
    },
    defaultVariants: {
      variant: "read",
    },
  },
);

export type ActivityType = "create" | "update" | "read" | "delete";

type ActivityBadgeProps = VariantProps<typeof activityBadgeVariants> & {
  className?: string;
  activity: ActivityType;
};

export const ActivityBadge = ({
  activity,
  className,
}: PropsWithChildren<ActivityBadgeProps>) => {
  return (
    <PillBadge
      className={cn(
        activityBadgeVariants({ variant: activity }),
        "rounded-2xl",
        className,
      )}
    >
      {snakeCaseToCapitalized(activity)}
    </PillBadge>
  );
};
