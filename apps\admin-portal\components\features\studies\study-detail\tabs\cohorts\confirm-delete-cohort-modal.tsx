import { <PERSON><PERSON>lert } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { CohortItem } from "@/lib/apis/cohorts";

import { useDeleteCohort } from "./hooks/use-cohorts-mutations";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedCohort: CohortItem;
};

export const ConfirmDeleteCohortModal = ({
  isOpen,
  onClose,
  selectedCohort,
}: Props) => {
  const { mutateAsync, isPending } = useDeleteCohort();

  const handleConfirmDelete = async () => {
    await mutateAsync(selectedCohort.id);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Delete Cohort</Modal.Header>
      <Modal.Body>
        <div className="flex flex-col items-center justify-center gap-2.5">
          <TriangleAlert className="text-red-500" size={34} />
          <span className="text-xl font-medium leading-[150%] dark:text-white">
            Are you sure you want to delete this Cohort?
          </span>
        </div>
      </Modal.Body>
      <Modal.Footer className="flex justify-end gap-2.5">
        <Button variant="outline" onClick={onClose} className="border-none">
          Cancel
        </Button>
        <Button
          variant="outline"
          onClick={handleConfirmDelete}
          disabled={isPending}
          isLoading={isPending}
        >
          Delete
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
