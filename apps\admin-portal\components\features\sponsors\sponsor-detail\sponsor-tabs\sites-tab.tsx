import { Card } from "flowbite-react";
import { useParams } from "next/navigation";

import { Table, TableLoading } from "@/components/ui/table";

import { useSponsorSites } from "../hooks/use-sponsor-sites";
import { columns } from "./columns/sites";

export const SitesTab = () => {
  const params = useParams();
  const { data, isPending } = useSponsorSites(params.id as string);

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 p-4 pb-0 text-lg font-semibold dark:text-gray-400">
        Sites
      </div>
      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <Table columns={columns} data={data?.results ?? []} />
      )}
    </Card>
  );
};
