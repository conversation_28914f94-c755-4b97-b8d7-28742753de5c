"use client";

import { Card } from "flowbite-react";
import { parseAsString, useQueryState } from "nuqs";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  TooltipContentProps,
} from "recharts";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";

import { Skeleton } from "@/components/ui/skeleton";
import { capitalize } from "@/utils/string";

import { useSourceDocumentStatusFunnel } from "../hooks/use-document-analytics-queries";

const STATUS_COLORS = {
  created: "#f59e0b",
  uploaded: "#3b82f6",
  processed: "#10b981",
  processing: "#ef4444",
  error: "#dc2626",
} as const;

const CustomTooltip = ({
  active,
  payload,
}: TooltipContentProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    const data = payload[0];

    return (
      <div className=" rounded-lg border bg-white p-3 shadow-md dark:border-gray-600 dark:bg-gray-800">
        <div className="flex items-center gap-2">
          <div
            className="h-3 w-3 rounded-full"
            style={{ backgroundColor: data.payload.fill }}
          />
          <span className="font-medium dark:text-white">
            {capitalize(data.payload.name)}
          </span>
        </div>
        <div className="text-muted-foreground mt-1 text-sm dark:text-white">
          Count:{" "}
          <span className="text-foreground font-medium">
            {data.value.toLocaleString()}
          </span>
        </div>
      </div>
    );
  }
  return null;
};

export const SourceDocumentStatusFunnel = () => {
  const [status, setStatus] = useQueryState("status", parseAsString);

  const { data, isPending } = useSourceDocumentStatusFunnel();

  const chartData =
    data
      ?.map((item) => ({
        name: item.processingStatus,
        value: item.count,
        fill:
          STATUS_COLORS[item.processingStatus as keyof typeof STATUS_COLORS] ||
          "#6b7280",
      }))
      .sort((a, b) => b.value - a.value) || [];

  return (
    <Card>
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
        Source Document Status
      </h3>
      {isPending ? (
        <Skeleton className="h-80 w-full" />
      ) : (
        <div className="h-80">
          {chartData.length === 0 ? (
            <div className="grid h-full place-content-center text-center">
              <p className="mt-2 text-lg text-gray-500 dark:text-gray-400">
                No source document status data available
              </p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <FunnelChart data={chartData}>
                <Tooltip content={CustomTooltip} />
                <Funnel
                  dataKey="value"
                  lastShapeType="rectangle"
                  stroke="#424242"
                  style={{
                    cursor: "pointer",
                  }}
                  onClick={(e) => {
                    setStatus(status === e.name ? null : e.name || null);
                  }}
                >
                  <LabelList
                    dataKey="name"
                    position="center"
                    fill="#fff"
                    stroke="none"
                    fontSize={12}
                    fontWeight="500"
                    formatter={(value) => capitalize(value?.toString())}
                  />
                </Funnel>
              </FunnelChart>
            </ResponsiveContainer>
          )}
        </div>
      )}
    </Card>
  );
};
