import { type ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import { ActiveStatusBadge } from "@/components/ui/badges";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { Activity } from "@/lib/apis/activities";

export const getColumns = (
  onEdit: (activity: Activity) => void,
): ColumnDef<Activity>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <span
        onClick={() => onEdit(row.original)}
        role="button"
        className="text-primary-500 hover:underline"
      >
        {row.getValue("name")}
      </span>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="line-clamp-2 text-gray-600 dark:text-gray-400">
        {row.getValue("description") || "No description"}
      </div>
    ),
  },
  {
    header: "System",
    accessorKey: "isSystem",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return (
        <PillBadge variant={value ? "success" : "default"}>
          {value ? "System" : "Custom"}
        </PillBadge>
      );
    },
  },
  {
    header: "Active",
    accessorKey: "isActive",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return <ActiveStatusBadge isActive={value} />;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const activity = row.original;
      return (
        <div
          className="text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
          onClick={() => onEdit(activity)}
        >
          <span className="whitespace-nowrap">Edit</span>
          <MdOutlineEdit />
        </div>
      );
    },
  },
];
