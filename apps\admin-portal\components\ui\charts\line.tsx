"use client";

import { useThemeMode } from "flowbite-react";
import {
  Cartesian<PERSON><PERSON>,
  <PERSON>,
  Line,
  LineC<PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TooltipContentProps,
  XAxis,
  YAxis,
} from "recharts";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";

export type LineConfig<K> = {
  dataKey: K;
  name: string;
  color?: string;
};

export type LineChartProps<T, K extends keyof T> = {
  data: T[];
  configs: LineConfig<K>[] | readonly LineConfig<K>[];
  dateKey?: K;
  showLegend?: boolean;
  showTooltip?: boolean;
  customTooltip?: React.ComponentType<TooltipContentProps<ValueType, NameType>>;
  className?: string;
  showGrid?: boolean;
  isPercentage?: boolean;
};

const defaultColors = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#F59E0B", // Yellow
  "#EF4444", // Red
  "#8B5CF6", // Purple
  "#F97316", // Orange
  "#06B6D4", // Cyan
  "#84CC16", // Lime
];

const DefaultTooltip = ({
  active,
  payload,
  label,
  isPercentage,
}: TooltipContentProps<ValueType, NameType> & { isPercentage?: boolean }) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg dark:border-gray-600 dark:bg-gray-800">
        <p className="font-medium text-gray-900 dark:text-white">{label}</p>
        <div className="mt-1 space-y-1">
          {payload.map((entry, index: number) => (
            <div key={index} className="flex justify-between gap-4 text-sm">
              <span style={{ color: entry.color }}>{entry.name}:</span>
              <span className="font-medium dark:text-white">
                {typeof entry.value === "number"
                  ? isPercentage
                    ? `${entry.value}%`
                    : entry.value.toLocaleString()
                  : entry.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

export const LineChart = <T, K extends Exclude<keyof T, symbol>>({
  data,
  configs,
  dateKey = "date" as K,
  showLegend = true,
  showTooltip = true,
  customTooltip,
  className = "h-80",
  showGrid = true,
  isPercentage = false,
}: LineChartProps<T, K>) => {
  const { mode } = useThemeMode();
  const configsWithColors = configs.map((config, index) => ({
    ...config,
    color: config.color || defaultColors[index % defaultColors.length],
  }));

  const TooltipComponent = customTooltip || DefaultTooltip;

  return (
    <div className={className}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart data={data}>
          <XAxis
            interval="preserveStartEnd"
            dataKey={dateKey}
            tick={{ fontSize: 12, fill: mode === "dark" ? "#fff" : "#000" }}
          />
          <YAxis
            tick={{ fontSize: 12, fill: mode === "dark" ? "#fff" : "#000" }}
            tickFormatter={(value) =>
              isPercentage ? `${value}%` : value.toLocaleString()
            }
            domain={isPercentage ? [0, 100] : undefined}
          />
          {showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              stroke={mode === "dark" ? "#374151" : "#e5e7eb"}
            />
          )}
          {showTooltip && (
            <Tooltip
              content={(props) => (
                <TooltipComponent {...props} isPercentage={isPercentage} />
              )}
            />
          )}
          {showLegend && <Legend />}
          {configsWithColors.map((config) => (
            <Line
              key={config.dataKey}
              type="monotone"
              dataKey={config.dataKey}
              stroke={config.color}
              strokeWidth={2}
              dot={true}
              name={config.name}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
};
