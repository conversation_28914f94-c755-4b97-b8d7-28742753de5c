import { use<PERSON>ara<PERSON> } from "next/navigation";
import { useState } from "react";
import { AiOutlineLoading } from "react-icons/ai";
import { IoMdAdd } from "react-icons/io";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Checkbox, Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazyMultipleSelect } from "@/components/ui/lazy-select/lazy-multiple-select";
import { Modal } from "@/components/ui/modal";
import { Table, TableLoading } from "@/components/ui/table";
import type { Activity } from "@/lib/apis/activities";
import { cn } from "@/lib/utils";

import { useInfiniteProcedures } from "../procedures/hooks/use-procedures-queries";
import { useActivityProcedures } from "../protocols/tabs/schedule-of-activities-tab/protocol-encounter-detail/hooks/use-activity-procedure";
import { generateActivityProceduresColumns } from "./activity-procedures";
import {
  useAddActivityMutation,
  useAddProcedureToActivity,
  useEditActivity,
  useRemoveProcedureFromActivity,
} from "./hooks/use-activities-mutations";

export const activitySchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type ActivityFormValues = z.infer<typeof activitySchema>;

type ModalActivityProps = {
  isOpen: boolean;
  onClose: () => void;
  activity?: Activity;
};

export const ModalActivity = ({
  isOpen,
  onClose,
  activity,
}: ModalActivityProps) => {
  const params = useParams();
  const { mutateAsync: addActivity, isPending: isAdding } =
    useAddActivityMutation(params.id as string);
  const { mutateAsync: editActivity, isPending: isEditing } = useEditActivity(
    params.id as string,
  );
  const [isModalAddProcedureOpen, setIsModalAddProcedureOpen] = useState(false);

  // const formRef = useRef<FormActions<z.ZodType<ActivityFormValues>>>(null);

  const isEditingMode = Boolean(activity);

  async function onSubmit(data: ActivityFormValues) {
    const payload = {
      ...data,
      studyId: params.id as string,
      isSystem: true,
    };

    if (isEditingMode && activity) {
      await editActivity({ id: activity.id!, ...payload });
    } else {
      await addActivity(payload);
    }
    onClose();
  }

  return (
    <>
      <Modal
        show={isOpen}
        onClose={onClose}
        className={isEditingMode ? "[&>div]:max-w-6xl" : "[&>div]:max-w-2xl"}
      >
        <Modal.Header>
          {isEditingMode ? "Edit Activity" : "Add Activity"}
        </Modal.Header>
        <Modal.Body>
          <Form
            mode="onChange"
            schema={activitySchema}
            onSubmit={onSubmit}
            defaultValues={{
              name: activity?.name || "",
              description: activity?.description || "",
              isActive: isEditingMode ? activity?.isActive : true,
            }}
          >
            <ActivityForm isEditing={isEditingMode} />

            {isEditingMode && activity && (
              <div className="mt-8">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-lg font-semibold dark:text-white">
                    Procedures
                  </h3>
                  <Button
                    type="button"
                    variant="primary"
                    onClick={() => setIsModalAddProcedureOpen(true)}
                    className="w-fit"
                  >
                    <IoMdAdd />
                    Add Procedure
                  </Button>
                </div>

                <ProceduresList activityId={activity?.id} />
              </div>
            )}

            <div className="flex justify-end gap-5 border-none pt-4 ">
              <CloseButton onClose={onClose} />
              <Button
                type="submit"
                color="blue"
                isLoading={isAdding || isEditing}
              >
                Save
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>
      {isEditingMode && activity && isModalAddProcedureOpen && (
        <ModalAddProcedure
          activityId={activity.id}
          isOpen={isModalAddProcedureOpen}
          onClose={() => setIsModalAddProcedureOpen(false)}
        />
      )}
    </>
  );
};

const ActivityForm = ({ isEditing }: { isEditing: boolean }) => {
  return (
    <div className="grid grid-cols-1 gap-3 sm:gap-6">
      <div className="space-y-1">
        <Label htmlFor="name">Name</Label>
        <InputField id="name" name="name" placeholder="Enter name..." />
      </div>
      <div className="space-y-1">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          placeholder="Enter description..."
        />
      </div>

      <div className={cn("flex items-center gap-2", !isEditing && "hidden")}>
        <Checkbox id="isActive" name="isActive" />
        <Label htmlFor="isActive">Active</Label>
      </div>
    </div>
  );
};

const ProceduresList = ({ activityId }: { activityId: string }) => {
  const { data, isLoading } = useActivityProcedures(activityId);
  const { mutateAsync, isPending } = useRemoveProcedureFromActivity();

  const columns = generateActivityProceduresColumns(
    undefined,
    async (id: string) => {
      await mutateAsync({
        activityId,
        procedureId: id,
      });
    },
  );

  if (isLoading) {
    return <TableLoading columns={columns} />;
  }

  return (
    <div
      className={cn("relative", isPending && "pointer-events-none opacity-40")}
    >
      {isPending && (
        <div className={"absolute inset-0 z-10 grid place-content-center"}>
          <AiOutlineLoading
            size={24}
            className=" animate-spin dark:fill-white"
          />
        </div>
      )}
      <Table columns={columns} data={data?.results || []} />
    </div>
  );
};

type ModalAddProcedureProps = {
  activityId: string;
  isOpen: boolean;
  onClose: () => void;
};

export const addProcedureToActivitySchema = z.object({
  procedures: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().nullable().optional(),
        isActive: z.boolean().optional(),
      }),
    )
    .min(1, "Procedure is required"),
});

type AddProcedureFormValues = z.infer<typeof addProcedureToActivitySchema>;

const ModalAddProcedure = ({
  activityId,
  isOpen,
  onClose,
}: ModalAddProcedureProps) => {
  const { data } = useActivityProcedures(activityId);

  const { mutateAsync: addProcedure, isPending: isAdding } =
    useAddProcedureToActivity();

  async function onSubmit(data: AddProcedureFormValues) {
    await addProcedure({
      activityId,
      procedureIds: data.procedures.map((pro) => pro.id),
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Procedure to Activity</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addProcedureToActivitySchema}
          onSubmit={onSubmit}
          defaultValues={{
            procedures: [],
          }}
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-1">
              <Label htmlFor="procedures">Procedure</Label>
              <LazyMultipleSelect
                id="procedures"
                name="procedures"
                placeholder="Select procedures..."
                searchPlaceholder="Search procedures..."
                useInfiniteQuery={useInfiniteProcedures}
                getOptionLabel={(option) => option.name}
                getOptionValue={(option) => option.id}
                mapData={(options) => {
                  return options.filter(
                    (opt) => !data?.results.some((pro) => pro.id === opt.id),
                  );
                }}
              />
            </div>

            <div className="flex flex-col justify-end gap-5 border-none pt-0 sm:flex-row">
              <CloseButton onClose={onClose} />
              <Button type="submit" color="blue" isLoading={isAdding}>
                Add
              </Button>
            </div>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
